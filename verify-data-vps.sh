#!/bin/bash

# Data Verification Script for goqr.info VPS
# Usage: ./verify-data-vps.sh

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 goqr.info Data Verification"
echo "============================="

# Check current directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "Not in goqr.info directory. Please run from /var/goqr.info"
    exit 1
fi

print_status "Current directory: $(pwd)"
print_status "Timestamp: $(date)"

# 1. Check MongoDB data files
print_status "Checking MongoDB data files..."
if [ -d "data/mongo" ]; then
    MONGO_SIZE=$(du -sh data/mongo | cut -f1)
    FILE_COUNT=$(find data/mongo -type f | wc -l)
    print_success "MongoDB data directory exists ($MONGO_SIZE, $FILE_COUNT files)"
    
    # Check for key MongoDB files
    if [ -f "data/mongo/WiredTiger.wt" ]; then
        print_success "WiredTiger storage engine files found"
    else
        print_warning "WiredTiger files not found"
    fi
else
    print_error "MongoDB data directory not found!"
fi

# 2. Check Docker containers
print_status "Checking Docker containers..."
if command -v docker &> /dev/null; then
    RUNNING_CONTAINERS=$(docker ps --format "{{.Names}}" | grep goqrinfo | wc -l)
    print_status "Running goqr.info containers: $RUNNING_CONTAINERS"
    
    if [ $RUNNING_CONTAINERS -gt 0 ]; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep goqrinfo
    else
        print_warning "No goqr.info containers running"
    fi
else
    print_error "Docker not found"
fi

# 3. Test MongoDB connection and data
print_status "Testing MongoDB connection..."
if docker ps | grep -q goqrinfo-mongodb-1; then
    # Test connection
    if docker exec goqrinfo-mongodb-1 mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
        print_success "MongoDB connection successful"
        
        # Get database statistics
        print_status "Database statistics:"
        
        # URL Shortener database
        URL_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use urlshortener; db.urls.countDocuments()" 2>/dev/null || echo "0")
        USER_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use urlshortener; db.users.countDocuments()" 2>/dev/null || echo "0")
        
        echo "  📊 URL Shortener Database:"
        echo "     🔗 URLs: $URL_COUNT"
        echo "     👥 Users: $USER_COUNT"
        
        # API database
        API_USER_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use goqr_api; db.users.countDocuments()" 2>/dev/null || echo "0")
        API_URL_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use goqr_api; db.urls.countDocuments()" 2>/dev/null || echo "0")
        
        echo "  📊 API Database:"
        echo "     👥 API Users: $API_USER_COUNT"
        echo "     🔗 API URLs: $API_URL_COUNT"
        
        # Total clicks calculation
        TOTAL_CLICKS=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "
        use urlshortener;
        db.urls.aggregate([
            { \$group: { _id: null, totalClicks: { \$sum: '\$clickCount' } } }
        ]).forEach(function(doc) { print(doc.totalClicks || 0); });
        " 2>/dev/null | tail -1 || echo "0")
        
        echo "  👆 Total Clicks: $TOTAL_CLICKS"
        
    else
        print_error "Cannot connect to MongoDB"
    fi
else
    print_warning "MongoDB container not running"
fi

# 4. Test web services
print_status "Testing web services..."

# Test main website
if curl -f -s http://localhost:3000 >/dev/null; then
    print_success "Main website (port 3000) is accessible"
else
    print_warning "Main website not accessible"
fi

# Test API service
if curl -f -s http://localhost:4000/api/health >/dev/null; then
    print_success "API service (port 4000) is accessible"
    
    # Get API health details
    API_HEALTH=$(curl -s http://localhost:4000/api/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    print_status "API health status: $API_HEALTH"
else
    print_warning "API service not accessible"
fi

# 5. Check disk space
print_status "Checking disk space..."
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 80 ]; then
    print_success "Disk usage: ${DISK_USAGE}% (healthy)"
elif [ $DISK_USAGE -lt 90 ]; then
    print_warning "Disk usage: ${DISK_USAGE}% (monitor closely)"
else
    print_error "Disk usage: ${DISK_USAGE}% (critical - cleanup needed)"
fi

# 6. Check NGINX configuration
print_status "Checking NGINX configuration..."
if [ -f "/etc/nginx/sites-available/goqr.info" ]; then
    print_success "NGINX configuration file exists"
    
    if nginx -t >/dev/null 2>&1; then
        print_success "NGINX configuration is valid"
    else
        print_error "NGINX configuration has errors"
    fi
    
    if systemctl is-active --quiet nginx; then
        print_success "NGINX service is running"
    else
        print_warning "NGINX service is not running"
    fi
else
    print_warning "NGINX configuration file not found"
fi

# 7. Check recent backups
print_status "Checking recent backups..."
if [ -d "/var/backups/goqr.info" ]; then
    RECENT_BACKUP=$(find /var/backups/goqr.info -name "backup_*" -type d -mtime -7 | wc -l)
    if [ $RECENT_BACKUP -gt 0 ]; then
        print_success "Found $RECENT_BACKUP recent backup(s) (last 7 days)"
        ls -la /var/backups/goqr.info/backup_* 2>/dev/null | tail -3
    else
        print_warning "No recent backups found"
    fi
else
    print_warning "Backup directory not found"
fi

# 8. System resources
print_status "System resources:"
echo "  💾 Memory: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
echo "  🖥️  CPU Load: $(uptime | awk -F'load average:' '{print $2}')"
echo "  ⏱️  Uptime: $(uptime -p)"

# Summary
echo ""
echo "📋 Verification Summary"
echo "======================"

# Count issues
ISSUES=0
if [ ! -d "data/mongo" ]; then ((ISSUES++)); fi
if [ $RUNNING_CONTAINERS -eq 0 ]; then ((ISSUES++)); fi
if [ $DISK_USAGE -gt 90 ]; then ((ISSUES++)); fi

if [ $ISSUES -eq 0 ]; then
    print_success "✅ All checks passed! Your goqr.info system is healthy."
else
    print_warning "⚠️  Found $ISSUES issue(s) that need attention."
fi

echo ""
echo "🔧 Quick commands:"
echo "  View logs: docker-compose logs -f"
echo "  Restart services: docker-compose restart"
echo "  Create backup: sudo ./backup-mongodb-vps.sh"
echo "  Monitor containers: watch docker-compose ps"
