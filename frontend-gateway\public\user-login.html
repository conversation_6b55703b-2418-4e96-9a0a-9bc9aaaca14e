<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign In - goqr.info</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .glass-effect {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
  <div class="container mx-auto px-4">
    <div class="max-w-md mx-auto">
      <!-- Logo -->
      <div class="text-center mb-8">
        <div class="flex items-center justify-center mb-4">
          <!-- SVG Logo -->
          <svg width="80" height="80" viewBox="0 0 200 220" xmlns="http://www.w3.org/2000/svg">
            <!-- Shield Background -->
            <path d="M100 10 L170 50 L170 130 Q170 180 100 210 Q30 180 30 130 L30 50 Z" 
                  fill="rgba(255,255,255,0.9)" stroke="none"/>
            
            <!-- QR Code Pattern -->
            <!-- Top Left Corner -->
            <rect x="50" y="40" width="35" height="35" fill="#667eea" rx="2"/>
            <rect x="55" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
            <rect x="60" y="50" width="15" height="15" fill="#667eea" rx="1"/>
            
            <!-- Top Right Corner -->
            <rect x="115" y="40" width="35" height="35" fill="#667eea" rx="2"/>
            <rect x="120" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
            <rect x="125" y="50" width="15" height="15" fill="#667eea" rx="1"/>
            
            <!-- Bottom Left Corner -->
            <rect x="50" y="105" width="35" height="35" fill="#667eea" rx="2"/>
            <rect x="55" y="110" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
            <rect x="60" y="115" width="15" height="15" fill="#667eea" rx="1"/>
            
            <!-- Center Pattern -->
            <rect x="95" y="85" width="10" height="10" fill="#667eea"/>
            <rect x="110" y="85" width="10" height="10" fill="#667eea"/>
            <rect x="125" y="85" width="10" height="10" fill="#667eea"/>
            <rect x="95" y="100" width="10" height="10" fill="#667eea"/>
            <rect x="110" y="100" width="10" height="10" fill="#667eea"/>
            <rect x="125" y="100" width="10" height="10" fill="#667eea"/>
            <rect x="95" y="115" width="10" height="10" fill="#667eea"/>
            <rect x="110" y="115" width="25" height="10" fill="#667eea"/>
            <rect x="95" y="130" width="40" height="10" fill="#667eea"/>
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">Welcome Back</h1>
        <p class="text-white text-opacity-90">Sign in to your goqr.info account</p>
      </div>

      <!-- Login Form -->
      <div class="glass-effect rounded-2xl p-8 shadow-2xl">
        <form id="loginForm" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Email or Username</label>
            <input type="text" id="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter your email or username">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
            <input type="password" id="password" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter your password">
          </div>
          
          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
              <span class="ml-2 text-sm text-gray-600">Remember me</span>
            </label>
            <a href="#" class="text-sm text-blue-600 hover:text-blue-800">Forgot password?</a>
          </div>
          
          <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-purple-700 transition duration-200 transform hover:scale-105">
            🔑 Sign In
          </button>
        </form>
        
        <div class="mt-6 text-center">
          <p class="text-gray-600">Don't have an account? 
            <a href="/register.html" class="text-blue-600 hover:text-blue-800 font-semibold">Sign Up</a>
          </p>
        </div>
        
        <div class="mt-4 text-center">
          <a href="/login.html" class="text-sm text-gray-500 hover:text-gray-700">
            🔧 Admin Login
          </a>
        </div>
        
        <!-- Success/Error Messages -->
        <div id="message" class="mt-4 p-4 rounded-lg hidden"></div>
      </div>

      <!-- Features Preview -->
      <div class="mt-8 glass-effect rounded-xl p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 text-center">Why Choose goqr.info?</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="text-center">
            <div class="text-2xl mb-2">🔗</div>
            <p class="text-gray-700">Short URLs</p>
          </div>
          <div class="text-center">
            <div class="text-2xl mb-2">📱</div>
            <p class="text-gray-700">QR Codes</p>
          </div>
          <div class="text-center">
            <div class="text-2xl mb-2">📊</div>
            <p class="text-gray-700">Analytics</p>
          </div>
          <div class="text-center">
            <div class="text-2xl mb-2">🛡️</div>
            <p class="text-gray-700">Secure</p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="text-center mt-6">
        <a href="/" class="text-white hover:text-gray-200 transition duration-200">
          ← Back to Home
        </a>
      </div>
    </div>
  </div>

  <script>
    // Check if user is already logged in
    if (localStorage.getItem('userToken')) {
      window.location.href = '/dashboard.html';
    }

    document.getElementById('loginForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const messageDiv = document.getElementById('message');
      const submitButton = e.target.querySelector('button[type="submit"]');
      
      // Get form data
      const formData = {
        email: document.getElementById('email').value,
        password: document.getElementById('password').value
      };
      
      // Disable submit button
      submitButton.disabled = true;
      submitButton.textContent = '🔄 Signing In...';
      
      try {
        const response = await fetch('/api/user/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
          // Success
          messageDiv.className = 'mt-4 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
          messageDiv.textContent = '🎉 Welcome back! Redirecting to dashboard...';
          messageDiv.classList.remove('hidden');
          
          // Store token
          localStorage.setItem('userToken', data.token);
          
          // Redirect to dashboard
          setTimeout(() => {
            window.location.href = '/dashboard.html';
          }, 1500);
          
        } else {
          // Error
          messageDiv.className = 'mt-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
          messageDiv.textContent = '❌ ' + data.error;
          messageDiv.classList.remove('hidden');
        }
        
      } catch (error) {
        messageDiv.className = 'mt-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
        messageDiv.textContent = '❌ Network error. Please try again.';
        messageDiv.classList.remove('hidden');
      }
      
      // Re-enable submit button
      submitButton.disabled = false;
      submitButton.textContent = '🔑 Sign In';
    });
  </script>
</body>
</html>
