const express = require('express');
const User = require('../models/User');
const { userAuth } = require('../middlewares/userAuth');
const router = express.Router();

// Subscription plans configuration
const SUBSCRIPTION_PLANS = {
  free: {
    name: 'Free Starter',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: [
      '50 URLs per month',
      'Basic QR codes',
      '30-day analytics',
      'Community support'
    ],
    limits: {
      monthlyUrls: 50,
      monthlyQrCodes: 50,
      monthlyApiCalls: 0,
      customDomains: 0,
      analyticsRetention: 30,
      teamMembers: 1
    },
    features_enabled: {
      customQrDesign: false,
      advancedAnalytics: false,
      apiAccess: false,
      customDomains: false,
      whiteLabel: false,
      prioritySupport: false
    }
  },
  pro: {
    name: 'Pro',
    price: 9.99,
    currency: 'USD',
    interval: 'month',
    stripeProductId: 'prod_pro_monthly', // Replace with actual Stripe product ID
    stripePriceId: 'price_pro_monthly',   // Replace with actual Stripe price ID
    features: [
      'Unlimited URLs',
      'Custom QR designs',
      '1-year analytics',
      'API access (10K requests)',
      'Custom short domains',
      'Priority support'
    ],
    limits: {
      monthlyUrls: -1,
      monthlyQrCodes: -1,
      monthlyApiCalls: 10000,
      customDomains: 3,
      analyticsRetention: 365,
      teamMembers: 5
    },
    features_enabled: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: false,
      prioritySupport: true
    }
  },
  business: {
    name: 'Business',
    price: 29.99,
    currency: 'USD',
    interval: 'month',
    stripeProductId: 'prod_business_monthly',
    stripePriceId: 'price_business_monthly',
    features: [
      'Everything in Pro',
      'API access (50K requests)',
      'Team collaboration (20 members)',
      'White-label options',
      'Advanced analytics',
      'Priority support'
    ],
    limits: {
      monthlyUrls: -1,
      monthlyQrCodes: -1,
      monthlyApiCalls: 50000,
      customDomains: 10,
      analyticsRetention: 365,
      teamMembers: 20
    },
    features_enabled: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: true,
      prioritySupport: true
    }
  },
  enterprise: {
    name: 'Enterprise',
    price: 99.99,
    currency: 'USD',
    interval: 'month',
    stripeProductId: 'prod_enterprise_monthly',
    stripePriceId: 'price_enterprise_monthly',
    features: [
      'Everything in Business',
      'Unlimited API requests',
      'Unlimited team members',
      'Custom integrations',
      'Dedicated support',
      'SLA guarantees'
    ],
    limits: {
      monthlyUrls: -1,
      monthlyQrCodes: -1,
      monthlyApiCalls: -1,
      customDomains: -1,
      analyticsRetention: -1,
      teamMembers: -1
    },
    features_enabled: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: true,
      prioritySupport: true
    }
  }
};

// Get all subscription plans
router.get('/plans', (req, res) => {
  res.json({
    plans: SUBSCRIPTION_PLANS
  });
});

// Get current user's subscription
router.get('/current', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const currentPlan = SUBSCRIPTION_PLANS[user.subscriptionTier];

    res.json({
      subscription: {
        tier: user.subscriptionTier,
        status: user.subscriptionStatus,
        startDate: user.subscriptionStartDate,
        endDate: user.subscriptionEndDate,
        plan: currentPlan,
        usage: user.monthlyUsage,
        limits: user.limits,
        features: user.features
      }
    });

  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Upgrade subscription (simplified version without Stripe for now)
router.post('/upgrade', userAuth, async (req, res) => {
  try {
    const { tier } = req.body;

    if (!tier || !SUBSCRIPTION_PLANS[tier]) {
      return res.status(400).json({ error: 'Invalid subscription tier' });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // For demo purposes, allow direct upgrade without payment
    // In production, this would integrate with Stripe
    const plan = SUBSCRIPTION_PLANS[tier];
    
    user.subscriptionTier = tier;
    user.subscriptionStatus = 'active';
    user.subscriptionStartDate = new Date();
    user.subscriptionEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
    user.limits = plan.limits;
    user.features = plan.features_enabled;
    user.updatedAt = new Date();

    await user.save();

    res.json({
      success: true,
      message: `Successfully upgraded to ${plan.name}`,
      subscription: {
        tier: user.subscriptionTier,
        status: user.subscriptionStatus,
        startDate: user.subscriptionStartDate,
        endDate: user.subscriptionEndDate,
        plan: plan,
        limits: user.limits,
        features: user.features
      }
    });

  } catch (error) {
    console.error('Upgrade subscription error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Cancel subscription
router.post('/cancel', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.subscriptionTier === 'free') {
      return res.status(400).json({ error: 'Cannot cancel free subscription' });
    }

    // Mark subscription as cancelled but keep active until end date
    user.subscriptionStatus = 'cancelled';
    user.updatedAt = new Date();

    await user.save();

    res.json({
      success: true,
      message: 'Subscription cancelled successfully',
      subscription: {
        tier: user.subscriptionTier,
        status: user.subscriptionStatus,
        endDate: user.subscriptionEndDate,
        message: 'Your subscription will remain active until the end of the billing period'
      }
    });

  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get subscription usage analytics
router.get('/usage-analytics', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const usage = user.monthlyUsage;
    const limits = user.limits;

    // Calculate usage percentages
    const analytics = {
      urls: {
        used: usage.urlsCreated,
        limit: limits.monthlyUrls,
        percentage: limits.monthlyUrls === -1 ? 0 : 
                   Math.round((usage.urlsCreated / limits.monthlyUrls) * 100),
        unlimited: limits.monthlyUrls === -1
      },
      qrCodes: {
        used: usage.qrCodesGenerated,
        limit: limits.monthlyQrCodes,
        percentage: limits.monthlyQrCodes === -1 ? 0 : 
                   Math.round((usage.qrCodesGenerated / limits.monthlyQrCodes) * 100),
        unlimited: limits.monthlyQrCodes === -1
      },
      apiCalls: {
        used: usage.apiCalls,
        limit: limits.monthlyApiCalls,
        percentage: limits.monthlyApiCalls === -1 ? 0 : 
                   Math.round((usage.apiCalls / limits.monthlyApiCalls) * 100),
        unlimited: limits.monthlyApiCalls === -1
      }
    };

    // Calculate days until reset
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const daysUntilReset = Math.ceil((nextMonth - now) / (1000 * 60 * 60 * 24));

    res.json({
      analytics,
      subscriptionTier: user.subscriptionTier,
      resetDate: nextMonth,
      daysUntilReset,
      lastResetDate: usage.lastResetDate
    });

  } catch (error) {
    console.error('Usage analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get upgrade recommendations
router.get('/recommendations', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const usage = user.monthlyUsage;
    const limits = user.limits;
    const currentTier = user.subscriptionTier;

    let recommendations = [];

    // Check if user is approaching limits
    if (currentTier === 'free') {
      const urlUsagePercent = (usage.urlsCreated / limits.monthlyUrls) * 100;
      
      if (urlUsagePercent > 80) {
        recommendations.push({
          type: 'upgrade',
          reason: 'approaching_url_limit',
          message: 'You\'re approaching your monthly URL limit. Upgrade to Pro for unlimited URLs!',
          suggestedTier: 'pro',
          urgency: urlUsagePercent > 95 ? 'high' : 'medium'
        });
      }

      if (usage.apiCalls > 0) {
        recommendations.push({
          type: 'upgrade',
          reason: 'api_usage_detected',
          message: 'API access is not available in the free plan. Upgrade to Pro for API access!',
          suggestedTier: 'pro',
          urgency: 'medium'
        });
      }
    }

    if (currentTier === 'pro') {
      const apiUsagePercent = (usage.apiCalls / limits.monthlyApiCalls) * 100;
      
      if (apiUsagePercent > 80) {
        recommendations.push({
          type: 'upgrade',
          reason: 'approaching_api_limit',
          message: 'You\'re approaching your monthly API limit. Upgrade to Business for 50K API calls!',
          suggestedTier: 'business',
          urgency: apiUsagePercent > 95 ? 'high' : 'medium'
        });
      }
    }

    res.json({
      recommendations,
      currentTier,
      usage,
      availableUpgrades: Object.keys(SUBSCRIPTION_PLANS).filter(tier => 
        ['pro', 'business', 'enterprise'].includes(tier) && tier !== currentTier
      )
    });

  } catch (error) {
    console.error('Recommendations error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
