const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const User = require('../models/User');
const { userAuth } = require('../middlewares/userAuth');
const router = express.Router();

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Generate API key
function generateApiKey() {
  return 'gqr_' + crypto.randomBytes(32).toString('hex');
}

// Generate referral code
function generateReferralCode(username) {
  return username.toUpperCase() + crypto.randomBytes(4).toString('hex').toUpperCase();
}

// Register new user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName, company, referralCode } = req.body;

    // Validation
    if (!username || !email || !password) {
      return res.status(400).json({ 
        error: 'Username, email, and password are required' 
      });
    }

    if (password.length < 6) {
      return res.status(400).json({ 
        error: 'Password must be at least 6 characters long' 
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ 
      $or: [{ email }, { username }] 
    });

    if (existingUser) {
      return res.status(400).json({ 
        error: 'User with this email or username already exists' 
      });
    }

    // Handle referral
    let referredBy = null;
    if (referralCode) {
      referredBy = await User.findOne({ referralCode });
      if (referredBy) {
        referredBy.referralCount += 1;
        await referredBy.save();
      }
    }

    // Create new user
    const user = new User({
      username,
      email,
      passwordHash: password, // Will be hashed by pre-save middleware
      firstName,
      lastName,
      company,
      referredBy: referredBy?._id,
      referralCode: generateReferralCode(username),
      apiKey: generateApiKey(),
      apiKeyCreatedAt: new Date(),
      emailVerificationToken: crypto.randomBytes(32).toString('hex')
    });

    // Set subscription limits and features
    const limits = User.getSubscriptionLimits('free');
    const features = User.getSubscriptionFeatures('free');
    user.limits = limits;
    user.features = features;

    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        role: 'user'
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        features: user.features,
        limits: user.limits,
        usage: user.monthlyUsage
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        error: 'Email and password are required' 
      });
    }

    // Find user by email or username
    const user = await User.findOne({ 
      $or: [{ email }, { username: email }] 
    });

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({ error: 'Account is deactivated' });
    }

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        role: 'user'
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        features: user.features,
        limits: user.limits,
        usage: user.monthlyUsage,
        apiKey: user.apiKey
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get current user profile
router.get('/profile', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-passwordHash');
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        company: user.company,
        subscriptionTier: user.subscriptionTier,
        subscriptionStatus: user.subscriptionStatus,
        features: user.features,
        limits: user.limits,
        usage: user.monthlyUsage,
        apiKey: user.apiKey,
        referralCode: user.referralCode,
        referralCount: user.referralCount,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile
router.put('/profile', userAuth, async (req, res) => {
  try {
    const { firstName, lastName, company } = req.body;
    
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update allowed fields
    if (firstName !== undefined) user.firstName = firstName;
    if (lastName !== undefined) user.lastName = lastName;
    if (company !== undefined) user.company = company;
    
    user.updatedAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        company: user.company
      }
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user usage statistics
router.get('/usage', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if usage needs to be reset
    if (user.hasExceededLimit('urls')) {
      // This will reset if needed
    }

    res.json({
      usage: user.monthlyUsage,
      limits: user.limits,
      subscriptionTier: user.subscriptionTier,
      percentageUsed: {
        urls: user.limits.monthlyUrls === -1 ? 0 : 
              Math.round((user.monthlyUsage.urlsCreated / user.limits.monthlyUrls) * 100),
        qrCodes: user.limits.monthlyQrCodes === -1 ? 0 : 
                 Math.round((user.monthlyUsage.qrCodesGenerated / user.limits.monthlyQrCodes) * 100),
        apiCalls: user.limits.monthlyApiCalls === -1 ? 0 : 
                  Math.round((user.monthlyUsage.apiCalls / user.limits.monthlyApiCalls) * 100)
      }
    });

  } catch (error) {
    console.error('Usage fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Regenerate API key
router.post('/regenerate-api-key', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    user.apiKey = generateApiKey();
    user.apiKeyCreatedAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'API key regenerated successfully',
      apiKey: user.apiKey
    });

  } catch (error) {
    console.error('API key regeneration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout (client-side token removal)
router.post('/logout', userAuth, (req, res) => {
  res.json({ 
    success: true, 
    message: 'Logged out successfully' 
  });
});

module.exports = router;
