const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const QRCode = require('qrcode');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { nanoid } = require('nanoid');

const app = express();
const PORT = 4001; // Different port to avoid conflict

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// In-memory storage for demo
const users = new Map();
const urls = new Map();
let userIdCounter = 1;
let urlIdCounter = 1;

const JWT_SECRET = 'demo-jwt-secret-2024';

// Helper functions
const generateApiKey = () => 'gqr_' + nanoid(32);
const generateShortCode = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Authentication middleware
const authenticate = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const apiKey = req.headers['x-api-key'];
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      const user = users.get(decoded.userId);
      if (user) {
        req.user = user;
        return next();
      }
    } catch (error) {
      // Token invalid
    }
  } else if (apiKey) {
    for (const [id, user] of users) {
      if (user.apiKey === apiKey) {
        req.user = user;
        return next();
      }
    }
  }
  
  return res.status(401).json({
    error: 'Authentication required',
    message: 'Please provide a valid JWT token or API key'
  });
};

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'goqr.info API Demo',
    version: '1.0.0',
    uptime: process.uptime(),
    database: { status: 'mock', connection: 'in-memory' },
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  });
});

// Detailed health check
app.get('/api/health/detailed', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: {
      name: 'goqr.info API Demo',
      version: '1.0.0',
      uptime: process.uptime(),
      environment: 'demo'
    },
    database: { status: 'mock', connection: 'in-memory' },
    statistics: {
      totalUsers: users.size,
      totalUrls: urls.size,
      totalClicks: Array.from(urls.values()).reduce((sum, url) => sum + url.clickCount, 0),
      activeUrls: Array.from(urls.values()).filter(url => url.isActive).length
    },
    system: {
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    },
    responseTime: 5
  });
});

// User registration
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName } = req.body;
    
    if (!username || !email || !password) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Username, email, and password are required'
      });
    }
    
    // Check if user exists
    for (const [id, user] of users) {
      if (user.email === email || user.username === username) {
        return res.status(409).json({
          error: 'User already exists',
          message: 'A user with this email or username already exists'
        });
      }
    }
    
    const userId = userIdCounter++;
    const passwordHash = await bcrypt.hash(password, 10);
    const apiKey = generateApiKey();
    
    const user = {
      id: userId,
      username,
      email,
      passwordHash,
      firstName: firstName || '',
      lastName: lastName || '',
      apiKey,
      subscriptionTier: 'free',
      subscriptionStatus: 'active',
      monthlyUsage: {
        urlsCreated: 0,
        qrCodesGenerated: 0,
        apiCalls: 0,
        lastResetDate: new Date()
      },
      limits: {
        monthlyUrls: 50,
        monthlyQrCodes: 50,
        monthlyApiCalls: 1000
      },
      features: {
        customQrDesign: false,
        apiAccess: true,
        advancedAnalytics: false
      },
      createdAt: new Date(),
      lastLoginAt: new Date()
    };
    
    users.set(userId, user);
    
    const token = jwt.sign({
      userId,
      username,
      email,
      subscriptionTier: 'free'
    }, JWT_SECRET, { expiresIn: '7d' });
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: userId,
        username,
        email,
        firstName: firstName || '',
        lastName: lastName || '',
        subscriptionTier: 'free',
        apiKey,
        features: user.features,
        limits: user.limits,
        createdAt: user.createdAt
      }
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'Internal server error'
    });
  }
});

// User login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Email and password are required'
      });
    }
    
    let user = null;
    for (const [id, u] of users) {
      if (u.email === email) {
        user = u;
        break;
      }
    }
    
    if (!user || !(await bcrypt.compare(password, user.passwordHash))) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }
    
    user.lastLoginAt = new Date();
    
    const token = jwt.sign({
      userId: user.id,
      username: user.username,
      email: user.email,
      subscriptionTier: user.subscriptionTier
    }, JWT_SECRET, { expiresIn: '7d' });
    
    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        subscriptionTier: user.subscriptionTier,
        apiKey: user.apiKey,
        features: user.features,
        limits: user.limits,
        lastLoginAt: user.lastLoginAt
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'Internal server error'
    });
  }
});

// Get user profile
app.get('/api/auth/profile', authenticate, (req, res) => {
  res.json({
    user: {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      subscriptionTier: req.user.subscriptionTier,
      subscriptionStatus: req.user.subscriptionStatus,
      apiKey: req.user.apiKey,
      features: req.user.features,
      limits: req.user.limits,
      usage: req.user.monthlyUsage,
      createdAt: req.user.createdAt,
      lastLoginAt: req.user.lastLoginAt
    }
  });
});

// Create short URL
app.post('/api/urls/shorten', authenticate, async (req, res) => {
  try {
    const { url, customAlias, title, description, tags, category } = req.body;
    
    if (!url) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'URL is required'
      });
    }
    
    // Check usage limits
    if (req.user.monthlyUsage.urlsCreated >= req.user.limits.monthlyUrls) {
      return res.status(429).json({
        error: 'Usage limit exceeded',
        message: 'You have reached your monthly URL limit'
      });
    }
    
    // Check if custom alias is taken
    if (customAlias) {
      for (const [id, existingUrl] of urls) {
        if (existingUrl.shortCode === customAlias) {
          return res.status(409).json({
            error: 'Alias unavailable',
            message: 'The custom alias is already taken'
          });
        }
      }
    }
    
    // Generate QR code
    const qrCode = await QRCode.toDataURL(url, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      width: 256,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    const urlId = urlIdCounter++;
    const shortCode = customAlias || generateShortCode();
    const shortUrl = `https://goqr.info/${shortCode}`;
    
    const urlDoc = {
      id: urlId,
      originalUrl: url,
      shortCode,
      shortUrl,
      qrCode,
      isCustom: !!customAlias,
      userId: req.user.id,
      title: title || '',
      description: description || '',
      tags: tags || [],
      category: category || '',
      clickCount: 0,
      isActive: true,
      isSafe: true,
      safetyScore: 100,
      createdAt: new Date()
    };
    
    urls.set(urlId, urlDoc);
    
    // Update user usage
    req.user.monthlyUsage.urlsCreated++;
    req.user.monthlyUsage.apiCalls++;
    
    const remainingUrls = req.user.limits.monthlyUrls - req.user.monthlyUsage.urlsCreated;
    
    res.status(201).json({
      success: true,
      message: 'Short URL created successfully',
      data: {
        id: urlId,
        shortUrl,
        shortCode,
        originalUrl: url,
        qrCode,
        isCustom: !!customAlias,
        clickCount: 0,
        isSafe: true,
        safetyScore: 100,
        title: title || '',
        description: description || '',
        tags: tags || [],
        category: category || '',
        createdAt: urlDoc.createdAt
      },
      user: {
        username: req.user.username,
        subscriptionTier: req.user.subscriptionTier,
        remainingUrls: remainingUrls.toString()
      }
    });
    
  } catch (error) {
    console.error('URL shortening error:', error);
    res.status(500).json({
      error: 'URL shortening failed',
      message: 'Internal server error'
    });
  }
});

// Generate QR code
app.post('/api/qr/generate', authenticate, async (req, res) => {
  try {
    const { data, format = 'png', size = 256, darkColor = '#000000', lightColor = '#FFFFFF' } = req.body;
    
    if (!data) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Data is required'
      });
    }
    
    // Check usage limits
    if (req.user.monthlyUsage.qrCodesGenerated >= req.user.limits.monthlyQrCodes) {
      return res.status(429).json({
        error: 'Usage limit exceeded',
        message: 'You have reached your monthly QR code limit'
      });
    }
    
    const qrCode = await QRCode.toDataURL(data, {
      errorCorrectionLevel: 'M',
      type: `image/${format}`,
      width: size,
      color: {
        dark: darkColor,
        light: lightColor
      }
    });
    
    // Update user usage
    req.user.monthlyUsage.qrCodesGenerated++;
    req.user.monthlyUsage.apiCalls++;
    
    const remainingQrCodes = req.user.limits.monthlyQrCodes - req.user.monthlyUsage.qrCodesGenerated;
    
    res.status(201).json({
      success: true,
      message: 'QR code generated successfully',
      data: {
        qrCode,
        format,
        size,
        darkColor,
        lightColor,
        dataLength: data.length,
        generatedAt: new Date().toISOString()
      },
      user: {
        username: req.user.username,
        subscriptionTier: req.user.subscriptionTier,
        remainingQrCodes: remainingQrCodes.toString()
      }
    });
    
  } catch (error) {
    console.error('QR generation error:', error);
    res.status(500).json({
      error: 'QR code generation failed',
      message: 'Internal server error'
    });
  }
});

// Get user URLs
app.get('/api/urls', authenticate, (req, res) => {
  const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 20));
  const page = Math.max(1, parseInt(req.query.page) || 1);
  const skip = (page - 1) * limit;
  
  const userUrls = Array.from(urls.values())
    .filter(url => url.userId === req.user.id && url.isActive)
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(skip, skip + limit);
  
  const total = Array.from(urls.values()).filter(url => url.userId === req.user.id && url.isActive).length;
  const pages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      urls: userUrls,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      }
    }
  });
});

// Analytics overview
app.get('/api/analytics/overview', authenticate, (req, res) => {
  const days = Math.min(365, Math.max(1, parseInt(req.query.days) || 30));
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  const userUrls = Array.from(urls.values())
    .filter(url => url.userId === req.user.id && url.isActive && new Date(url.createdAt) >= cutoffDate);
  
  const totalUrls = userUrls.length;
  const totalClicks = userUrls.reduce((sum, url) => sum + url.clickCount, 0);
  const averageClicksPerUrl = totalUrls > 0 ? Math.round((totalClicks / totalUrls) * 100) / 100 : 0;
  
  const topPerformingUrl = userUrls.reduce((top, url) => 
    url.clickCount > (top?.clickCount || 0) ? url : top, null);
  
  const usage = req.user.monthlyUsage;
  const limits = req.user.limits;
  
  const percentageUsed = {
    urls: Math.round((usage.urlsCreated / limits.monthlyUrls) * 100),
    qrCodes: Math.round((usage.qrCodesGenerated / limits.monthlyQrCodes) * 100),
    apiCalls: Math.round((usage.apiCalls / limits.monthlyApiCalls) * 100)
  };
  
  res.json({
    success: true,
    data: {
      summary: {
        totalUrls,
        totalClicks,
        averageClicksPerUrl,
        topPerformingUrl: topPerformingUrl ? {
          shortCode: topPerformingUrl.shortCode,
          originalUrl: topPerformingUrl.originalUrl,
          title: topPerformingUrl.title,
          clickCount: topPerformingUrl.clickCount
        } : null
      },
      usage: {
        urlsCreated: usage.urlsCreated,
        qrCodesGenerated: usage.qrCodesGenerated,
        apiCalls: usage.apiCalls,
        percentageUsed,
        limits: {
          monthlyUrls: limits.monthlyUrls,
          monthlyQrCodes: limits.monthlyQrCodes,
          monthlyApiCalls: limits.monthlyApiCalls
        }
      },
      trends: {
        clicksByDate: {},
        urlsByDate: {}
      },
      period: {
        days,
        from: cutoffDate.toISOString(),
        to: new Date().toISOString()
      }
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'goqr.info API Demo',
    version: '1.0.0',
    status: 'active',
    documentation: 'See README.md for API documentation',
    endpoints: {
      auth: '/api/auth',
      urls: '/api/urls',
      qr: '/api/qr',
      analytics: '/api/analytics',
      health: '/api/health'
    },
    timestamp: new Date().toISOString(),
    demo: true,
    storage: 'in-memory'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 goqr.info API Demo running on port ${PORT}`);
  console.log(`🔗 Service Info: http://localhost:${PORT}/`);
  console.log(`💾 Storage: In-memory (demo mode)`);
  console.log(`📊 Users: ${users.size}, URLs: ${urls.size}`);
});

module.exports = app;
