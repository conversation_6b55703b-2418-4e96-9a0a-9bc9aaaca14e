const express = require('express');
const path = require('path');
const axios = require('axios');
const cors = require('cors');
const mongoose = require('mongoose');
const routes = require('./routes');
const adminRoutes = require('./routes/adminRoutes');
const userRoutes = require('./routes/userRoutes');
const subscriptionRoutes = require('./routes/subscriptionRoutes');


const app = express();

// Connect to MongoDB
mongoose.connect('mongodb://mongodb:27017/urlshortener', {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('✅ MongoDB connected for user management');
}).catch(err => {
  console.error('❌ MongoDB connection failed:', err.message);
});

// Middlewares
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// Routes
app.use('/api', routes);
app.use('/api/admin', adminRoutes);
app.use('/api/user', userRoutes);
app.use('/api/subscription', subscriptionRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'frontend-gateway',
    timestamp: new Date().toISOString()
  });
});

// Handle short URL redirects
app.get('/:code', async (req, res) => {
  const { code } = req.params;

  // Validate short code format (alphanumeric, hyphens, underscores, 2-50 characters)
  if (!/^[a-zA-Z0-9\-_]{2,50}$/.test(code)) {
    return res.redirect('/');
  }

  try {
    // Forward to URL shortener service for redirect with original headers
    const response = await axios.get(`http://url-shortener:4000/${code}`, {
      maxRedirects: 0,
      validateStatus: (status) => status >= 200 && status < 400,
      headers: {
        'User-Agent': req.get('User-Agent') || '',
        'Referer': req.get('Referer') || req.get('Referrer') || '',
        'Accept-Language': req.get('Accept-Language') || '',
        'X-Forwarded-For': req.ip || req.connection.remoteAddress || '',
        'X-Real-IP': req.ip || req.connection.remoteAddress || ''
      }
    });

    // If it's a redirect, follow it
    if (response.status >= 300 && response.status < 400) {
      return res.redirect(response.headers.location);
    }

    res.send(response.data);
  } catch (err) {
    if (err.response && err.response.status === 302) {
      return res.redirect(err.response.headers.location);
    }
    console.error('Redirect error:', err.message);
    res.redirect('/');
  }
});

const PORT = 3000;
app.listen(PORT, () => console.log(`Frontend Gateway running on port ${PORT}`));