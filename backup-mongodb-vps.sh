#!/bin/bash

# MongoDB Backup Script for goqr.info VPS
# Usage: sudo ./backup-mongodb-vps.sh

set -e

# Configuration
PROJECT_DIR="/var/goqr.info"
BACKUP_BASE_DIR="/var/backups/goqr.info"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="$BACKUP_BASE_DIR/backup_$TIMESTAMP"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🛡️  MongoDB Backup for goqr.info"
echo "================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    print_error "This script needs sudo privileges"
    exit 1
fi

# Create backup directories
mkdir -p $BACKUP_DIR
mkdir -p $BACKUP_BASE_DIR/logs

cd $PROJECT_DIR

print_status "Starting backup process..."
print_status "Backup location: $BACKUP_DIR"

# 1. File System Backup (MongoDB data files)
print_status "Backing up MongoDB data files..."
if [ -d "data/mongo" ]; then
    cp -r data/mongo $BACKUP_DIR/
    MONGO_SIZE=$(du -sh data/mongo | cut -f1)
    print_success "MongoDB files backed up ($MONGO_SIZE)"
else
    print_error "MongoDB data directory not found!"
    exit 1
fi

# 2. MongoDB Dump (if container is running)
print_status "Creating MongoDB dump..."
if docker ps | grep -q goqrinfo-mongodb-1; then
    # Create dump inside container
    docker exec goqrinfo-mongodb-1 mongodump --out /data/db/dump_$TIMESTAMP
    
    # Copy dump to backup directory
    docker cp goqrinfo-mongodb-1:/data/db/dump_$TIMESTAMP $BACKUP_DIR/mongodump
    
    # Clean up dump from container
    docker exec goqrinfo-mongodb-1 rm -rf /data/db/dump_$TIMESTAMP
    
    print_success "MongoDB dump created"
else
    print_warning "MongoDB container not running, skipping dump"
fi

# 3. Configuration Backup
print_status "Backing up configurations..."
cp docker-compose.yml $BACKUP_DIR/ 2>/dev/null || print_warning "docker-compose.yml not found"
cp nginx-goqr.conf $BACKUP_DIR/ 2>/dev/null || print_warning "nginx-goqr.conf not found"
cp -r frontend-gateway $BACKUP_DIR/ 2>/dev/null || print_warning "frontend-gateway not found"

# Copy environment files
find . -name ".env*" -exec cp {} $BACKUP_DIR/ \; 2>/dev/null || true

# 4. Database Statistics
print_status "Collecting database statistics..."
if docker ps | grep -q goqrinfo-mongodb-1; then
    cat > $BACKUP_DIR/db_stats.txt << EOF
Database Statistics - $(date)
================================

Databases:
$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "show dbs" 2>/dev/null || echo "Could not connect")

URL Shortener Database:
$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "
use urlshortener;
print('Collections:');
show collections;
print('\\nDocument counts:');
print('URLs: ' + db.urls.countDocuments());
print('Users: ' + db.users.countDocuments());
print('Analytics: ' + (db.analytics ? db.analytics.countDocuments() : 'N/A'));
" 2>/dev/null || echo "Could not get database stats")

API Database:
$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "
use goqr_api;
print('Collections:');
show collections;
print('\\nDocument counts:');
db.getCollectionNames().forEach(function(collection) {
    print(collection + ': ' + db[collection].countDocuments());
});
" 2>/dev/null || echo "Could not get API database stats")
EOF
    print_success "Database statistics saved"
fi

# 5. System Information
print_status "Collecting system information..."
cat > $BACKUP_DIR/system_info.txt << EOF
System Backup Information
========================
Backup created: $(date)
Hostname: $(hostname)
Ubuntu version: $(lsb_release -d | cut -f2)
Docker version: $(docker --version)
Docker Compose version: $(docker-compose --version)

Project directory: $PROJECT_DIR
Backup directory: $BACKUP_DIR

Disk usage:
$(df -h $PROJECT_DIR)

MongoDB data size: $(du -sh data/mongo 2>/dev/null | cut -f1 || echo "N/A")
Total backup size: $(du -sh $BACKUP_DIR | cut -f1)

Running containers:
$(docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}")

Docker images:
$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}")
EOF

# 6. Create compressed archive
print_status "Creating compressed archive..."
cd $BACKUP_BASE_DIR
tar -czf "goqr_backup_$TIMESTAMP.tar.gz" "backup_$TIMESTAMP"
ARCHIVE_SIZE=$(du -sh "goqr_backup_$TIMESTAMP.tar.gz" | cut -f1)
print_success "Compressed archive created: goqr_backup_$TIMESTAMP.tar.gz ($ARCHIVE_SIZE)"

# 7. Cleanup old backups (keep last 7 days)
print_status "Cleaning up old backups..."
find $BACKUP_BASE_DIR -name "backup_*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
find $BACKUP_BASE_DIR -name "goqr_backup_*.tar.gz" -mtime +7 -delete 2>/dev/null || true
print_success "Old backups cleaned up"

# 8. Log the backup
echo "$(date): Backup completed - $BACKUP_DIR ($ARCHIVE_SIZE)" >> $BACKUP_BASE_DIR/logs/backup.log

# Summary
echo ""
echo "🎉 Backup completed successfully!"
echo "================================"
print_success "Backup directory: $BACKUP_DIR"
print_success "Compressed archive: $BACKUP_BASE_DIR/goqr_backup_$TIMESTAMP.tar.gz"
print_success "Archive size: $ARCHIVE_SIZE"
print_status "Backup log: $BACKUP_BASE_DIR/logs/backup.log"

echo ""
echo "📋 Backup contents:"
ls -la $BACKUP_DIR

echo ""
echo "🔄 To restore from this backup:"
echo "1. Stop containers: docker-compose down"
echo "2. Restore data: rm -rf data/mongo && cp -r $BACKUP_DIR/mongo data/"
echo "3. Start containers: docker-compose up -d"

echo ""
echo "📦 To download backup:"
echo "scp user@your-server:$BACKUP_BASE_DIR/goqr_backup_$TIMESTAMP.tar.gz ."
