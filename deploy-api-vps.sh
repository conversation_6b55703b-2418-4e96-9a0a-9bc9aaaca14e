#!/bin/bash

# goqr.info API Service Deployment Script for Ubuntu VPS
# Path: /var/goqr.info

set -e  # Exit on any error

echo "🚀 Starting goqr.info API Service Deployment"
echo "📍 Current directory: $(pwd)"
echo "🕐 Timestamp: $(date)"

# Configuration
PROJECT_DIR="/var/goqr.info"
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
API_SERVICE_DIR="$PROJECT_DIR/api-service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root or with sudo
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script needs to be run with sudo privileges"
        print_status "Please run: sudo $0"
        exit 1
    fi
}

# Function to backup existing data
backup_data() {
    print_status "Creating backup of existing data..."
    
    cd $PROJECT_DIR
    mkdir -p $BACKUP_DIR
    
    # Backup MongoDB data (most critical)
    if [ -d "data" ]; then
        cp -r data/ $BACKUP_DIR/
        print_success "MongoDB data backed up"
    else
        print_warning "No data directory found"
    fi
    
    # Backup configurations
    cp docker-compose.yml $BACKUP_DIR/ 2>/dev/null || print_warning "docker-compose.yml not found"
    cp nginx-goqr.conf $BACKUP_DIR/ 2>/dev/null || print_warning "nginx-goqr.conf not found"
    cp -r frontend-gateway/ $BACKUP_DIR/ 2>/dev/null || print_warning "frontend-gateway not found"
    
    # Create backup info
    cat > $BACKUP_DIR/backup_info.txt << EOF
Backup created: $(date)
Original path: $PROJECT_DIR
MongoDB data size: $(du -sh data/ 2>/dev/null | cut -f1 || echo "N/A")
Total backup size: $(du -sh $BACKUP_DIR | cut -f1)
Docker containers before backup:
$(docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker not running")
EOF
    
    print_success "Backup completed in: $PROJECT_DIR/$BACKUP_DIR"
}

# Function to check current system status
check_system() {
    print_status "Checking system status..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if containers are running
    if docker ps | grep -q goqrinfo; then
        print_status "Found running goqr.info containers"
        docker ps --format "table {{.Names}}\t{{.Status}}" | grep goqrinfo
    else
        print_warning "No goqr.info containers currently running"
    fi
    
    # Check MongoDB data
    if [ -d "$PROJECT_DIR/data/mongo" ]; then
        MONGO_SIZE=$(du -sh $PROJECT_DIR/data/mongo | cut -f1)
        print_status "MongoDB data size: $MONGO_SIZE"
    else
        print_warning "MongoDB data directory not found"
    fi
    
    print_success "System check completed"
}

# Function to create API service directory structure
setup_api_service() {
    print_status "Setting up API service directory..."
    
    mkdir -p $API_SERVICE_DIR/src/{routes,models,middleware}
    
    # Create package.json if it doesn't exist
    if [ ! -f "$API_SERVICE_DIR/package.json" ]; then
        cat > $API_SERVICE_DIR/package.json << 'EOF'
{
  "name": "goqr-api-service",
  "version": "1.0.0",
  "description": "Dedicated API service for goqr.info backend connectivity",
  "main": "src/server.js",
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "mongoose": "^8.0.0",
    "bcrypt": "^6.0.0",
    "jsonwebtoken": "^9.0.2",
    "nanoid": "^3.3.7",
    "qrcode": "^1.5.3",
    "axios": "^1.6.8",
    "joi": "^17.11.0",
    "swagger-jsdoc": "^6.2.8",
    "swagger-ui-express": "^5.0.0",
    "dotenv": "^16.3.1"
  },
  "keywords": ["api", "url-shortener", "qr-code", "backend", "microservice"],
  "author": "goqr.info",
  "license": "MIT"
}
EOF
        print_success "Created package.json"
    fi
    
    # Create Dockerfile if it doesn't exist
    if [ ! -f "$API_SERVICE_DIR/Dockerfile" ]; then
        cat > $API_SERVICE_DIR/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

COPY src/ ./src/

RUN addgroup -g 1001 -S nodejs && \
    adduser -S apiuser -u 1001
RUN chown -R apiuser:nodejs /app
USER apiuser

EXPOSE 4000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4000/api/health/live', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

CMD ["node", "src/server.js"]
EOF
        print_success "Created Dockerfile"
    fi
    
    print_success "API service directory structure ready"
}

# Function to deploy the API service
deploy_api() {
    print_status "Deploying API service..."
    
    cd $PROJECT_DIR
    
    # Stop existing containers gracefully
    print_status "Stopping existing containers..."
    docker-compose down
    
    # Build the new API service
    print_status "Building API service..."
    if docker-compose build api-service; then
        print_success "API service built successfully"
    else
        print_error "Failed to build API service"
        exit 1
    fi
    
    # Start all services
    print_status "Starting all services..."
    if docker-compose up -d; then
        print_success "All services started"
    else
        print_error "Failed to start services"
        exit 1
    fi
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check service status
    docker-compose ps
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    # Check if all containers are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Containers are running"
    else
        print_error "Some containers are not running"
        docker-compose ps
    fi
    
    # Test API service health
    print_status "Testing API service health..."
    sleep 5
    
    if curl -f http://localhost:4000/api/health >/dev/null 2>&1; then
        print_success "API service is healthy"
    else
        print_warning "API service health check failed (this is normal if MongoDB is not connected yet)"
    fi
    
    # Test main website
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        print_success "Main website is accessible"
    else
        print_warning "Main website is not accessible"
    fi
    
    # Check MongoDB data integrity
    if docker exec goqrinfo-mongodb-1 mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
        print_success "MongoDB is accessible"
        
        # Count documents
        URL_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use urlshortener; db.urls.countDocuments()" 2>/dev/null || echo "0")
        USER_COUNT=$(docker exec goqrinfo-mongodb-1 mongosh --quiet --eval "use urlshortener; db.users.countDocuments()" 2>/dev/null || echo "0")
        
        print_status "Data integrity check:"
        print_status "  URLs: $URL_COUNT"
        print_status "  Users: $USER_COUNT"
    else
        print_warning "MongoDB connection check failed"
    fi
}

# Function to update NGINX configuration
update_nginx() {
    print_status "Checking NGINX configuration..."
    
    if [ -f "/etc/nginx/sites-available/goqr.info" ]; then
        print_status "NGINX configuration found"
        
        # Check if API proxy is already configured
        if grep -q "location /api" /etc/nginx/sites-available/goqr.info; then
            print_success "API proxy already configured in NGINX"
        else
            print_warning "API proxy not found in NGINX config"
            print_status "You may need to add API proxy configuration manually"
        fi
    else
        print_warning "NGINX configuration file not found"
    fi
}

# Main execution
main() {
    echo "🚀 goqr.info API Service Deployment"
    echo "=================================="
    
    check_permissions
    check_system
    backup_data
    setup_api_service
    deploy_api
    verify_deployment
    update_nginx
    
    echo ""
    echo "🎉 Deployment completed!"
    echo "=================================="
    print_success "Backup location: $PROJECT_DIR/$BACKUP_DIR"
    print_success "API service: http://your-domain:4000"
    print_success "Main website: http://your-domain:3000"
    print_status "Check logs with: docker-compose logs -f api-service"
    print_status "Monitor all services: docker-compose ps"
    
    echo ""
    echo "📋 Next steps:"
    echo "1. Upload your API service source files to: $API_SERVICE_DIR/src/"
    echo "2. Configure NGINX proxy for /api routes"
    echo "3. Test the API endpoints"
    echo "4. Update DNS if needed"
}

# Run main function
main "$@"
