const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const UserSchema = new mongoose.Schema({
  // Basic Info
  username: { 
    type: String, 
    required: true, 
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: { 
    type: String, 
    required: true, 
    unique: true,
    trim: true,
    lowercase: true
  },
  passwordHash: { 
    type: String, 
    required: true 
  },
  
  // Profile
  firstName: String,
  lastName: String,
  company: String,
  
  // Subscription Info
  subscriptionTier: {
    type: String,
    enum: ['free', 'pro', 'business', 'enterprise'],
    default: 'free'
  },
  subscriptionStatus: {
    type: String,
    enum: ['active', 'inactive', 'cancelled', 'past_due'],
    default: 'active'
  },
  subscriptionStartDate: Date,
  subscriptionEndDate: Date,
  stripeCustomerId: String,
  stripeSubscriptionId: String,
  
  // Usage Tracking
  monthlyUsage: {
    urlsCreated: { type: Number, default: 0 },
    qrCodesGenerated: { type: Number, default: 0 },
    apiCalls: { type: Number, default: 0 },
    lastResetDate: { type: Date, default: Date.now }
  },
  
  // Limits based on subscription
  limits: {
    monthlyUrls: { type: Number, default: 50 },
    monthlyQrCodes: { type: Number, default: 50 },
    monthlyApiCalls: { type: Number, default: 0 },
    customDomains: { type: Number, default: 0 },
    analyticsRetention: { type: Number, default: 30 }, // days
    teamMembers: { type: Number, default: 1 }
  },
  
  // Features
  features: {
    customQrDesign: { type: Boolean, default: false },
    advancedAnalytics: { type: Boolean, default: false },
    apiAccess: { type: Boolean, default: false },
    customDomains: { type: Boolean, default: false },
    whiteLabel: { type: Boolean, default: false },
    prioritySupport: { type: Boolean, default: false }
  },
  
  // Account Status
  isActive: { type: Boolean, default: true },
  isEmailVerified: { type: Boolean, default: false },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastLoginAt: Date,
  
  // API Keys
  apiKey: String,
  apiKeyCreatedAt: Date,
  
  // Referral System
  referralCode: String,
  referredBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  referralCount: { type: Number, default: 0 }
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ username: 1 });
UserSchema.index({ subscriptionTier: 1 });
UserSchema.index({ stripeCustomerId: 1 });
UserSchema.index({ apiKey: 1 });
UserSchema.index({ referralCode: 1 });

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('passwordHash')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.passwordHash = await bcrypt.hash(this.passwordHash, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.passwordHash);
};

// Method to check if user has exceeded usage limits
UserSchema.methods.hasExceededLimit = function(type) {
  const usage = this.monthlyUsage;
  const limits = this.limits;
  
  // Reset monthly usage if it's a new month
  const now = new Date();
  const lastReset = new Date(usage.lastResetDate);
  if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
    this.monthlyUsage.urlsCreated = 0;
    this.monthlyUsage.qrCodesGenerated = 0;
    this.monthlyUsage.apiCalls = 0;
    this.monthlyUsage.lastResetDate = now;
  }
  
  switch (type) {
    case 'urls':
      return usage.urlsCreated >= limits.monthlyUrls;
    case 'qr':
      return usage.qrCodesGenerated >= limits.monthlyQrCodes;
    case 'api':
      return usage.apiCalls >= limits.monthlyApiCalls;
    default:
      return false;
  }
};

// Method to increment usage
UserSchema.methods.incrementUsage = function(type) {
  switch (type) {
    case 'urls':
      this.monthlyUsage.urlsCreated += 1;
      break;
    case 'qr':
      this.monthlyUsage.qrCodesGenerated += 1;
      break;
    case 'api':
      this.monthlyUsage.apiCalls += 1;
      break;
  }
  this.updatedAt = new Date();
};

// Static method to get subscription limits
UserSchema.statics.getSubscriptionLimits = function(tier) {
  const limits = {
    free: {
      monthlyUrls: 50,
      monthlyQrCodes: 50,
      monthlyApiCalls: 0,
      customDomains: 0,
      analyticsRetention: 30,
      teamMembers: 1
    },
    pro: {
      monthlyUrls: -1, // unlimited
      monthlyQrCodes: -1,
      monthlyApiCalls: 10000,
      customDomains: 3,
      analyticsRetention: 365,
      teamMembers: 5
    },
    business: {
      monthlyUrls: -1,
      monthlyQrCodes: -1,
      monthlyApiCalls: 50000,
      customDomains: 10,
      analyticsRetention: 365,
      teamMembers: 20
    },
    enterprise: {
      monthlyUrls: -1,
      monthlyQrCodes: -1,
      monthlyApiCalls: -1,
      customDomains: -1,
      analyticsRetention: -1,
      teamMembers: -1
    }
  };
  
  return limits[tier] || limits.free;
};

// Static method to get subscription features
UserSchema.statics.getSubscriptionFeatures = function(tier) {
  const features = {
    free: {
      customQrDesign: false,
      advancedAnalytics: false,
      apiAccess: false,
      customDomains: false,
      whiteLabel: false,
      prioritySupport: false
    },
    pro: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: false,
      prioritySupport: true
    },
    business: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: true,
      prioritySupport: true
    },
    enterprise: {
      customQrDesign: true,
      advancedAnalytics: true,
      apiAccess: true,
      customDomains: true,
      whiteLabel: true,
      prioritySupport: true
    }
  };
  
  return features[tier] || features.free;
};

module.exports = mongoose.model('User', UserSchema);
