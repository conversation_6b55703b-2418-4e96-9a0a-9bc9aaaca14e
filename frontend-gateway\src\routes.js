const express = require('express');
const axios = require('axios');
const router = express.Router();

// Import user authentication and middleware
const { optionalAuth, checkUsageLimit, userAuth } = require('./middlewares/userAuth');

const SHORTEN_SERVICE = 'http://url-shortener:4000';
const QRCODE_SERVICE = 'http://qrcode-generator:4001';
const URLCHECK_SERVICE = 'http://url-checker:4002';
const SCANNER_SERVICE = 'http://scanner-service:8082';

console.log("🚀 Routes loaded with user authentication");

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'frontend-gateway',
    timestamp: new Date().toISOString()
  });
});



router.post('/shorten', optionalAuth, checkUsageLimit('urls'), async (req, res) => {
  const { url, customCode, allowDuplicates } = req.body;
  console.log("📥 POST /shorten received:", { url, customCode, allowDuplicates });
  console.log("👤 User:", req.user ? `${req.user.username} (${req.user.subscriptionTier})` : 'Anonymous');

  if (!url) {
    console.log("❌ No URL provided");
    return res.status(400).json({ error: 'URL is required' });
  }

  try {
    console.log("🔎 Checking URL safety...");
    const check = await axios.post(`${URLCHECK_SERVICE}/check`, { url });
    console.log("✅ Safe check result:", check.data);

    console.log("🔗 Creating short URL...");
    const shortRes = await axios.post(`${SHORTEN_SERVICE}/shorten`, {
      url,
      customCode,
      allowDuplicates
    });
    console.log("✅ Shortened:", shortRes.data);

    const shortUrl = shortRes.data.shortUrl;

    console.log("🖨️ Generating QR...");
    const qrRes = await axios.post(`${QRCODE_SERVICE}/generate`, { url: shortUrl });
    console.log("✅ QR Code:", qrRes.data);

    const qrCode = qrRes.data.qrCode || qrRes.data.qrImage;

    res.json({
      shortUrl,
      qrCode,
      shortCode: shortRes.data.shortCode,
      isCustom: shortRes.data.isCustom,
      message: shortRes.data.message,
      user: req.user ? {
        username: req.user.username,
        subscriptionTier: req.user.subscriptionTier,
        remainingUrls: req.user.subscriptionTier === 'free' ? 'Check /api/user/usage' : 'Unlimited'
      } : null
    });
  } catch (err) {
    console.error("❌ Error during shortening process:", err.message);
    if (err.response) {
      console.error("↪️ Response data:", err.response.data);
      console.error("↪️ Status code:", err.response.status);
    }
    res.status(500).json({
      error: err.response?.data?.error || 'Something went wrong.'
    });
  }
});





router.get('/admin/urls', async (req, res) => {
  try {
    const result = await axios.get('http://url-shortener:4000/admin/urls');
    res.json(result.data);
  } catch (err) {
    console.error('Failed to fetch URLs:', err.message);
    res.status(500).json({ error: 'Failed to fetch URLs' });
  }
});

router.delete('/admin/delete/:code', async (req, res) => {
  const { code } = req.params;
  try {
    await axios.delete(`http://url-shortener:4000/admin/delete/${code}`);
    res.json({ success: true, message: 'URL deleted successfully' });
  } catch (err) {
    console.error('Failed to delete short code:', err.message);
    res.status(500).json({
      error: err.response?.data?.error || 'Delete failed'
    });
  }
});

// Advanced URL scanning endpoint
router.post('/scan', async (req, res) => {
  const { url } = req.body;
  console.log("🔍 POST /scan received:", url);

  if (!url) {
    console.log("❌ No URL provided for scanning");
    return res.status(400).json({ error: 'URL is required' });
  }

  try {
    console.log("🛡️ Performing advanced URL scan...");
    const scanResult = await axios.get(`${SCANNER_SERVICE}/scan-url`, {
      params: { url }
    });
    console.log("✅ Scan result:", scanResult.data);

    res.json(scanResult.data);
  } catch (err) {
    console.error("❌ Error during URL scanning:", err.message);
    if (err.response) {
      console.error("↪️ Response data:", err.response.data);
      console.error("↪️ Status code:", err.response.status);
    }
    res.status(500).json({
      error: err.response?.data?.error || 'URL scanning failed'
    });
  }
});

// QR Code generation endpoint
router.post('/qr', optionalAuth, checkUsageLimit('qr'), async (req, res) => {
  const { url } = req.body;
  console.log("📱 POST /qr received:", url);
  console.log("👤 User:", req.user ? `${req.user.username} (${req.user.subscriptionTier})` : 'Anonymous');

  if (!url) {
    console.log("❌ No URL provided for QR generation");
    return res.status(400).json({ error: 'URL is required' });
  }

  try {
    console.log("🖨️ Generating QR code...");
    const qrRes = await axios.post(`${QRCODE_SERVICE}/generate`, { url });
    console.log("✅ QR Code generated:", qrRes.data);

    // Return the QR code URL for download
    res.json({
      qrCodeUrl: qrRes.data.qrCode || qrRes.data.qrImage,
      url: url
    });
  } catch (err) {
    console.error("❌ Error during QR generation:", err.message);
    if (err.response) {
      console.error("↪️ Response data:", err.response.data);
      console.error("↪️ Status code:", err.response.status);
    }
    res.status(500).json({
      error: err.response?.data?.error || 'QR code generation failed'
    });
  }
});

// Import auth middleware
const auth = require('./middlewares/auth');

// Analytics endpoints (protected)
router.get('/analytics', auth, async (req, res) => {
  try {
    const result = await axios.get(`${SHORTEN_SERVICE}/analytics`, {
      params: req.query
    });
    res.json(result.data);
  } catch (err) {
    console.error('Failed to fetch analytics:', err.message);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

router.get('/analytics/:code', auth, async (req, res) => {
  const { code } = req.params;
  try {
    const result = await axios.get(`${SHORTEN_SERVICE}/analytics/${code}`, {
      params: req.query
    });
    res.json(result.data);
  } catch (err) {
    console.error('Failed to fetch URL analytics:', err.message);
    res.status(500).json({ error: 'Failed to fetch URL analytics' });
  }
});

module.exports = router;
