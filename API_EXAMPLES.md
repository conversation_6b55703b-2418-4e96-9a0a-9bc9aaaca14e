# 🚀 goqr.info API - Complete Backend Integration Guide

## 📋 Overview

The goqr.info API provides comprehensive backend connectivity for URL shortening, QR code generation, and analytics. Perfect for integrating into your applications, websites, or services.

## 🔗 API Endpoints

### **Base URL**: `http://localhost:4001` (Demo) | `https://api.goqr.info` (Production)

---

## 🔐 Authentication

### 1. User Registration
```bash
curl -X POST http://localhost:4001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "myapp",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "firstName": "App",
    "lastName": "Developer"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "myapp",
    "email": "<EMAIL>",
    "subscriptionTier": "free",
    "apiKey": "gqr_abc123def456...",
    "limits": {
      "monthlyUrls": 50,
      "monthlyQrCodes": 50,
      "monthlyApiCalls": 1000
    }
  }
}
```

### 2. User Login
```bash
curl -X POST http://localhost:4001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

---

## 🔗 URL Shortening with QR Code

### Create Short URL + QR Code
```bash
curl -X POST http://localhost:4001/api/urls/shorten \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.example.com/very/long/url/path",
    "customAlias": "mylink",
    "title": "My Example Website",
    "description": "This is my awesome website",
    "tags": ["example", "website", "demo"],
    "category": "business"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Short URL created successfully",
  "data": {
    "id": 1,
    "shortUrl": "https://goqr.info/mylink",
    "shortCode": "mylink",
    "originalUrl": "https://www.example.com/very/long/url/path",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEA...",
    "isCustom": true,
    "clickCount": 0,
    "isSafe": true,
    "safetyScore": 100,
    "title": "My Example Website",
    "description": "This is my awesome website",
    "tags": ["example", "website", "demo"],
    "category": "business",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "user": {
    "username": "myapp",
    "subscriptionTier": "free",
    "remainingUrls": "49"
  }
}
```

### Using API Key Authentication
```bash
curl -X POST http://localhost:4001/api/urls/shorten \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.google.com",
    "title": "Google Search"
  }'
```

---

## 📱 QR Code Generation

### Generate Custom QR Code
```bash
curl -X POST http://localhost:4001/api/qr/generate \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "data": "https://goqr.info - The best URL shortener!",
    "format": "png",
    "size": 512,
    "darkColor": "#000000",
    "lightColor": "#FFFFFF"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "QR code generated successfully",
  "data": {
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIA...",
    "format": "png",
    "size": 512,
    "darkColor": "#000000",
    "lightColor": "#FFFFFF",
    "dataLength": 43,
    "generatedAt": "2024-01-15T10:30:00.000Z"
  },
  "user": {
    "username": "myapp",
    "subscriptionTier": "free",
    "remainingQrCodes": "49"
  }
}
```

---

## 📊 Analytics & Data

### Get User Analytics Overview
```bash
curl -X GET "http://localhost:4001/api/analytics/overview?days=30" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalUrls": 25,
      "totalClicks": 1250,
      "averageClicksPerUrl": 50,
      "topPerformingUrl": {
        "shortCode": "mylink",
        "originalUrl": "https://www.example.com",
        "title": "My Example Website",
        "clickCount": 150
      }
    },
    "usage": {
      "urlsCreated": 25,
      "qrCodesGenerated": 30,
      "apiCalls": 85,
      "percentageUsed": {
        "urls": 50,
        "qrCodes": 60,
        "apiCalls": 8
      },
      "limits": {
        "monthlyUrls": 50,
        "monthlyQrCodes": 50,
        "monthlyApiCalls": 1000
      }
    }
  }
}
```

### Get User's URLs
```bash
curl -X GET "http://localhost:4001/api/urls?limit=10&page=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🔧 Integration Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

class GoQRAPI {
  constructor(apiKey) {
    this.api = axios.create({
      baseURL: 'http://localhost:4001', // or https://api.goqr.info
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    });
  }

  async createShortUrl(url, options = {}) {
    try {
      const response = await this.api.post('/api/urls/shorten', {
        url,
        ...options
      });
      return response.data;
    } catch (error) {
      throw new Error(`API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  async generateQR(data, options = {}) {
    try {
      const response = await this.api.post('/api/qr/generate', {
        data,
        format: 'png',
        size: 256,
        ...options
      });
      return response.data;
    } catch (error) {
      throw new Error(`API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  async getAnalytics(days = 30) {
    try {
      const response = await this.api.get(`/api/analytics/overview?days=${days}`);
      return response.data;
    } catch (error) {
      throw new Error(`API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Usage Example
const goqr = new GoQRAPI('your-api-key-here');

async function example() {
  // Create short URL with QR code
  const result = await goqr.createShortUrl('https://www.example.com', {
    title: 'My Website',
    customAlias: 'mysite'
  });
  
  console.log('Short URL:', result.data.shortUrl);
  console.log('QR Code:', result.data.qrCode);
  
  // Generate standalone QR code
  const qr = await goqr.generateQR('Hello World!', {
    size: 512,
    darkColor: '#FF0000'
  });
  
  console.log('QR Code:', qr.data.qrCode);
  
  // Get analytics
  const analytics = await goqr.getAnalytics(7);
  console.log('Total URLs:', analytics.data.summary.totalUrls);
  console.log('Total Clicks:', analytics.data.summary.totalClicks);
}

example().catch(console.error);
```

### Python
```python
import requests
import base64
import json

class GoQRAPI:
    def __init__(self, api_key, base_url='http://localhost:4001'):
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def create_short_url(self, url, **options):
        """Create a short URL with QR code"""
        data = {'url': url, **options}
        response = requests.post(
            f'{self.base_url}/api/urls/shorten',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def generate_qr(self, data, **options):
        """Generate a QR code"""
        payload = {'data': data, **options}
        response = requests.post(
            f'{self.base_url}/api/qr/generate',
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def save_qr_image(self, qr_data_url, filename):
        """Save QR code to file"""
        # Remove data:image/png;base64, prefix
        image_data = qr_data_url.split(',')[1]
        with open(filename, 'wb') as f:
            f.write(base64.b64decode(image_data))
    
    def get_analytics(self, days=30):
        """Get analytics overview"""
        response = requests.get(
            f'{self.base_url}/api/analytics/overview?days={days}',
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage Example
api = GoQRAPI('your-api-key-here')

# Create short URL
result = api.create_short_url(
    'https://www.python.org',
    title='Python Official Website',
    customAlias='python',
    tags=['programming', 'python']
)

print(f"Short URL: {result['data']['shortUrl']}")
print(f"Clicks: {result['data']['clickCount']}")

# Save QR code to file
api.save_qr_image(result['data']['qrCode'], 'python_qr.png')
print("QR code saved to python_qr.png")

# Generate custom QR code
qr_result = api.generate_qr(
    'Visit https://goqr.info for the best URL shortener!',
    size=512,
    darkColor='#FF0000',
    lightColor='#FFFF00'
)

api.save_qr_image(qr_result['data']['qrCode'], 'custom_qr.png')
print("Custom QR code saved to custom_qr.png")

# Get analytics
analytics = api.get_analytics(7)
print(f"URLs created this week: {analytics['data']['summary']['totalUrls']}")
print(f"Total clicks: {analytics['data']['summary']['totalClicks']}")
```

### PHP
```php
<?php
class GoQRAPI {
    private $baseUrl;
    private $apiKey;
    
    public function __construct($apiKey, $baseUrl = 'http://localhost:4001') {
        $this->baseUrl = $baseUrl;
        $this->apiKey = $apiKey;
    }
    
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        $headers = [
            'X-API-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode >= 400) {
            throw new Exception("API Error: HTTP $httpCode - $response");
        }
        
        return json_decode($response, true);
    }
    
    public function createShortUrl($url, $options = []) {
        $data = array_merge(['url' => $url], $options);
        return $this->makeRequest('POST', '/api/urls/shorten', $data);
    }
    
    public function generateQR($data, $options = []) {
        $payload = array_merge(['data' => $data], $options);
        return $this->makeRequest('POST', '/api/qr/generate', $payload);
    }
    
    public function getAnalytics($days = 30) {
        return $this->makeRequest('GET', "/api/analytics/overview?days=$days");
    }
    
    public function saveQRImage($qrDataUrl, $filename) {
        // Remove data:image/png;base64, prefix
        $imageData = explode(',', $qrDataUrl)[1];
        file_put_contents($filename, base64_decode($imageData));
    }
}

// Usage Example
$api = new GoQRAPI('your-api-key-here');

try {
    // Create short URL
    $result = $api->createShortUrl('https://www.php.net', [
        'title' => 'PHP Official Website',
        'customAlias' => 'php',
        'tags' => ['programming', 'php']
    ]);
    
    echo "Short URL: " . $result['data']['shortUrl'] . "\n";
    echo "Clicks: " . $result['data']['clickCount'] . "\n";
    
    // Save QR code
    $api->saveQRImage($result['data']['qrCode'], 'php_qr.png');
    echo "QR code saved to php_qr.png\n";
    
    // Get analytics
    $analytics = $api->getAnalytics(7);
    echo "URLs created this week: " . $analytics['data']['summary']['totalUrls'] . "\n";
    echo "Total clicks: " . $analytics['data']['summary']['totalClicks'] . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
```

---

## 🔒 Security & Best Practices

### 1. **API Key Security**
- Store API keys in environment variables
- Never commit API keys to version control
- Rotate API keys regularly
- Use different API keys for different environments

### 2. **Rate Limiting**
- Respect rate limits (1000 requests/15 minutes)
- Implement exponential backoff for retries
- Monitor your usage through analytics

### 3. **Error Handling**
```javascript
try {
  const result = await api.post('/api/urls/shorten', data);
  return result.data;
} catch (error) {
  if (error.response?.status === 429) {
    // Rate limit exceeded
    console.log('Rate limit exceeded, waiting...');
    await new Promise(resolve => setTimeout(resolve, 60000));
    // Retry request
  } else if (error.response?.status === 401) {
    // Authentication failed
    console.log('Invalid API key');
  } else {
    console.log('API Error:', error.response?.data?.message);
  }
}
```

---

## 📊 Subscription Tiers & Limits

| Feature | Free | Pro | Business | Enterprise |
|---------|------|-----|----------|------------|
| **Monthly URLs** | 50 | Unlimited | Unlimited | Unlimited |
| **Monthly QR Codes** | 50 | Unlimited | Unlimited | Unlimited |
| **API Calls** | 1,000 | 10,000 | 50,000 | Unlimited |
| **Custom Aliases** | ✅ | ✅ | ✅ | ✅ |
| **Analytics** | 30 days | 1 year | 2 years | Unlimited |
| **Batch Operations** | ❌ | ✅ | ✅ | ✅ |
| **Priority Support** | ❌ | ✅ | ✅ | ✅ |

---

## 🚀 Getting Started

1. **Register for an account**: `POST /api/auth/register`
2. **Get your API key**: Check the registration response or profile endpoint
3. **Test the API**: Use the examples above
4. **Integrate**: Add the API to your application
5. **Monitor**: Check analytics and usage limits

---

## 📞 Support

- **API Status**: `GET /api/health`
- **Documentation**: This guide + API responses
- **Issues**: Check error codes and messages in API responses

**🎉 Happy coding with goqr.info API!**
