const axios = require('axios');

// API Configuration
const API_BASE_URL = 'http://localhost:4001'; // Demo API port
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000
});

// Test data
const testUser = {
  username: 'testapi',
  email: '<EMAIL>',
  password: 'password123',
  firstName: 'API',
  lastName: 'Tester'
};

let authToken = '';
let apiKey = '';

async function testAPI() {
  console.log('🚀 Starting goqr.info API Tests\n');
  
  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await api.get('/api/health');
    console.log('✅ Health Status:', healthResponse.data.status);
    console.log('📊 Database:', healthResponse.data.database.status);
    console.log('⏱️  Uptime:', Math.round(healthResponse.data.uptime), 'seconds\n');
    
    // Test 2: User Registration
    console.log('2️⃣ Testing User Registration...');
    try {
      const registerResponse = await api.post('/api/auth/register', testUser);
      authToken = registerResponse.data.token;
      apiKey = registerResponse.data.user.apiKey;
      console.log('✅ User registered successfully');
      console.log('👤 Username:', registerResponse.data.user.username);
      console.log('🎫 Subscription:', registerResponse.data.user.subscriptionTier);
      console.log('🔑 API Key:', apiKey.substring(0, 20) + '...\n');
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️  User already exists, attempting login...');
        const loginResponse = await api.post('/api/auth/login', {
          email: testUser.email,
          password: testUser.password
        });
        authToken = loginResponse.data.token;
        apiKey = loginResponse.data.user.apiKey;
        console.log('✅ Login successful');
        console.log('🔑 API Key:', apiKey.substring(0, 20) + '...\n');
      } else {
        throw error;
      }
    }
    
    // Test 3: Create Short URL with JWT
    console.log('3️⃣ Testing URL Shortening with JWT...');
    const urlResponse = await api.post('/api/urls/shorten', {
      url: 'https://www.google.com',
      title: 'Google Search',
      description: 'The world\'s most popular search engine',
      tags: ['search', 'google'],
      category: 'tools'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Short URL created successfully');
    console.log('🔗 Short URL:', urlResponse.data.data.shortUrl);
    console.log('📱 QR Code:', urlResponse.data.data.qrCode.substring(0, 50) + '...');
    console.log('👤 User:', urlResponse.data.user.username);
    console.log('📊 Remaining URLs:', urlResponse.data.user.remainingUrls, '\n');
    
    // Test 4: Generate QR Code with API Key
    console.log('4️⃣ Testing QR Code Generation with API Key...');
    const qrResponse = await api.post('/api/qr/generate', {
      data: 'https://goqr.info - The best URL shortener!',
      format: 'png',
      size: 256,
      darkColor: '#000000',
      lightColor: '#FFFFFF'
    }, {
      headers: { 'X-API-Key': apiKey }
    });
    
    console.log('✅ QR Code generated successfully');
    console.log('📱 Format:', qrResponse.data.data.format);
    console.log('📏 Size:', qrResponse.data.data.size + 'px');
    console.log('📊 Data Length:', qrResponse.data.data.dataLength, 'characters');
    console.log('👤 User:', qrResponse.data.user.username);
    console.log('📊 Remaining QR Codes:', qrResponse.data.user.remainingQrCodes, '\n');
    
    // Test 5: Get User Profile
    console.log('5️⃣ Testing User Profile...');
    const profileResponse = await api.get('/api/auth/profile', {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Profile retrieved successfully');
    console.log('👤 User:', profileResponse.data.user.username);
    console.log('📧 Email:', profileResponse.data.user.email);
    console.log('🎫 Subscription:', profileResponse.data.user.subscriptionTier);
    console.log('📊 Usage:', {
      urls: profileResponse.data.user.usage.urlsCreated,
      qrCodes: profileResponse.data.user.usage.qrCodesGenerated,
      apiCalls: profileResponse.data.user.usage.apiCalls
    });
    console.log('🔑 API Key:', profileResponse.data.user.apiKey.substring(0, 20) + '...\n');
    
    // Test 6: Get User URLs
    console.log('6️⃣ Testing URL List...');
    const urlsResponse = await api.get('/api/urls?limit=5', {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ URLs retrieved successfully');
    console.log('📊 Total URLs:', urlsResponse.data.data.pagination.total);
    console.log('🔗 URLs:');
    urlsResponse.data.data.urls.forEach((url, index) => {
      console.log(`   ${index + 1}. ${url.shortUrl} → ${url.originalUrl}`);
      console.log(`      👆 Clicks: ${url.clickCount} | Created: ${new Date(url.createdAt).toLocaleDateString()}`);
    });
    console.log('');
    
    // Test 7: Analytics Overview
    console.log('7️⃣ Testing Analytics...');
    const analyticsResponse = await api.get('/api/analytics/overview?days=30', {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Analytics retrieved successfully');
    console.log('📊 Summary:');
    console.log('   🔗 Total URLs:', analyticsResponse.data.data.summary.totalUrls);
    console.log('   👆 Total Clicks:', analyticsResponse.data.data.summary.totalClicks);
    console.log('   📈 Avg Clicks/URL:', analyticsResponse.data.data.summary.averageClicksPerUrl);
    console.log('📊 Usage:');
    console.log('   🔗 URLs Created:', analyticsResponse.data.data.usage.urlsCreated);
    console.log('   📱 QR Codes:', analyticsResponse.data.data.usage.qrCodesGenerated);
    console.log('   🔌 API Calls:', analyticsResponse.data.data.usage.apiCalls);
    console.log('');
    
    // Test 8: Detailed Health Check
    console.log('8️⃣ Testing Detailed Health Check...');
    const detailedHealthResponse = await api.get('/api/health/detailed');
    
    console.log('✅ Detailed health retrieved successfully');
    console.log('📊 Statistics:');
    console.log('   👥 Total Users:', detailedHealthResponse.data.statistics.totalUsers);
    console.log('   🔗 Total URLs:', detailedHealthResponse.data.statistics.totalUrls);
    console.log('   👆 Total Clicks:', detailedHealthResponse.data.statistics.totalClicks);
    console.log('   ✅ Active URLs:', detailedHealthResponse.data.statistics.activeUrls);
    console.log('💾 Memory Usage:', detailedHealthResponse.data.system.memory.used + 'MB');
    console.log('⏱️  Response Time:', detailedHealthResponse.data.responseTime + 'ms\n');
    
    console.log('🎉 All API tests completed successfully!');
    console.log('📚 Visit http://localhost:4000/api/docs for interactive documentation');
    
  } catch (error) {
    console.error('❌ API Test Error:', error.response?.data || error.message);
    console.error('📍 Status:', error.response?.status);
    console.error('🔗 URL:', error.config?.url);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
