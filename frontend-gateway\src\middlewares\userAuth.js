const jwt = require('jsonwebtoken');
const User = require('../models/User');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// User authentication middleware
const userAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Check if user still exists and is active
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // Add user info to request
    req.user = {
      userId: user._id,
      username: user.username,
      email: user.email,
      subscriptionTier: user.subscriptionTier,
      role: 'user'
    };

    next();

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    } else {
      console.error('Auth middleware error:', error);
      return res.status(401).json({ error: 'Token verification failed' });
    }
  }
};

// API key authentication middleware
const apiKeyAuth = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'] || req.query.api_key;

    if (!apiKey) {
      return res.status(401).json({ error: 'API key required' });
    }

    // Find user by API key
    const user = await User.findOne({ apiKey, isActive: true });
    if (!user) {
      return res.status(401).json({ error: 'Invalid API key' });
    }

    // Check if user has API access
    if (!user.features.apiAccess) {
      return res.status(403).json({ 
        error: 'API access not available in your subscription tier',
        upgradeUrl: '/pricing'
      });
    }

    // Add user info to request
    req.user = {
      userId: user._id,
      username: user.username,
      email: user.email,
      subscriptionTier: user.subscriptionTier,
      role: 'user'
    };

    next();

  } catch (error) {
    console.error('API key auth error:', error);
    return res.status(401).json({ error: 'API key verification failed' });
  }
};

// Usage limit middleware
const checkUsageLimit = (type) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.userId) {
        // If no user (anonymous), apply strict limits
        return res.status(401).json({ 
          error: 'Registration required for this feature',
          registerUrl: '/register'
        });
      }

      const user = await User.findById(req.user.userId);
      if (!user) {
        return res.status(401).json({ error: 'User not found' });
      }

      // Check if user has exceeded limit
      if (user.hasExceededLimit(type)) {
        const upgradeMessage = {
          free: 'Upgrade to Pro for unlimited usage',
          pro: 'Contact support for higher limits',
          business: 'Contact support for higher limits',
          enterprise: 'You have unlimited usage'
        };

        return res.status(429).json({ 
          error: `Monthly ${type} limit exceeded`,
          currentUsage: user.monthlyUsage,
          limits: user.limits,
          subscriptionTier: user.subscriptionTier,
          upgradeMessage: upgradeMessage[user.subscriptionTier],
          upgradeUrl: '/pricing'
        });
      }

      // Increment usage
      user.incrementUsage(type);
      await user.save();

      next();

    } catch (error) {
      console.error('Usage limit check error:', error);
      return res.status(500).json({ error: 'Usage limit check failed' });
    }
  };
};

// Subscription tier middleware
const requireSubscription = (minTier) => {
  const tierLevels = {
    free: 0,
    pro: 1,
    business: 2,
    enterprise: 3
  };

  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({ 
          error: 'Authentication required',
          loginUrl: '/login'
        });
      }

      const user = await User.findById(req.user.userId);
      if (!user) {
        return res.status(401).json({ error: 'User not found' });
      }

      const userTierLevel = tierLevels[user.subscriptionTier] || 0;
      const requiredTierLevel = tierLevels[minTier] || 0;

      if (userTierLevel < requiredTierLevel) {
        return res.status(403).json({ 
          error: `This feature requires ${minTier} subscription or higher`,
          currentTier: user.subscriptionTier,
          requiredTier: minTier,
          upgradeUrl: '/pricing'
        });
      }

      next();

    } catch (error) {
      console.error('Subscription check error:', error);
      return res.status(500).json({ error: 'Subscription check failed' });
    }
  };
};

// Optional authentication (for features that work better with auth but don't require it)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const apiKey = req.headers['x-api-key'] || req.query.api_key;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        const user = await User.findById(decoded.userId);
        if (user && user.isActive) {
          req.user = {
            userId: user._id,
            username: user.username,
            email: user.email,
            subscriptionTier: user.subscriptionTier,
            role: 'user'
          };
        }
      } catch (error) {
        // Ignore token errors for optional auth
      }
    } else if (apiKey) {
      try {
        const user = await User.findOne({ apiKey, isActive: true });
        if (user) {
          req.user = {
            userId: user._id,
            username: user.username,
            email: user.email,
            subscriptionTier: user.subscriptionTier,
            role: 'user'
          };
        }
      } catch (error) {
        // Ignore API key errors for optional auth
      }
    }

    next();

  } catch (error) {
    // For optional auth, always continue even if there's an error
    next();
  }
};

module.exports = {
  userAuth,
  apiKeyAuth,
  checkUsageLimit,
  requireSubscription,
  optionalAuth
};
