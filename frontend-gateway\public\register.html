<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign Up - goqr.info</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .glass-effect {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .feature-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body class="gradient-bg min-h-screen">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="flex items-center justify-center mb-4">
        <!-- SVG Logo -->
        <svg width="60" height="60" viewBox="0 0 200 220" xmlns="http://www.w3.org/2000/svg">
          <!-- Shield Background -->
          <path d="M100 10 L170 50 L170 130 Q170 180 100 210 Q30 180 30 130 L30 50 Z" 
                fill="rgba(255,255,255,0.9)" stroke="none"/>
          
          <!-- QR Code Pattern -->
          <!-- Top Left Corner -->
          <rect x="50" y="40" width="35" height="35" fill="#667eea" rx="2"/>
          <rect x="55" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
          <rect x="60" y="50" width="15" height="15" fill="#667eea" rx="1"/>
          
          <!-- Top Right Corner -->
          <rect x="115" y="40" width="35" height="35" fill="#667eea" rx="2"/>
          <rect x="120" y="45" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
          <rect x="125" y="50" width="15" height="15" fill="#667eea" rx="1"/>
          
          <!-- Bottom Left Corner -->
          <rect x="50" y="105" width="35" height="35" fill="#667eea" rx="2"/>
          <rect x="55" y="110" width="25" height="25" fill="rgba(255,255,255,0.9)" rx="1"/>
          <rect x="60" y="115" width="15" height="15" fill="#667eea" rx="1"/>
          
          <!-- Center Pattern -->
          <rect x="95" y="85" width="10" height="10" fill="#667eea"/>
          <rect x="110" y="85" width="10" height="10" fill="#667eea"/>
          <rect x="125" y="85" width="10" height="10" fill="#667eea"/>
          <rect x="95" y="100" width="10" height="10" fill="#667eea"/>
          <rect x="110" y="100" width="10" height="10" fill="#667eea"/>
          <rect x="125" y="100" width="10" height="10" fill="#667eea"/>
          <rect x="95" y="115" width="10" height="10" fill="#667eea"/>
          <rect x="110" y="115" width="25" height="10" fill="#667eea"/>
          <rect x="95" y="130" width="40" height="10" fill="#667eea"/>
        </svg>
      </div>
      <h1 class="text-4xl font-bold text-white mb-2">Join goqr.info</h1>
      <p class="text-white text-opacity-90">Create your account and start shortening URLs with QR codes</p>
    </div>

    <div class="max-w-6xl mx-auto grid md:grid-cols-2 gap-8">
      <!-- Registration Form -->
      <div class="glass-effect rounded-2xl p-8 shadow-2xl">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Create Account</h2>
        
        <form id="registerForm" class="space-y-4">
          <div class="grid md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
              <input type="text" id="firstName" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="John">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
              <input type="text" id="lastName" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Doe">
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
            <input type="text" id="username" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="johndoe">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
            <input type="email" id="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
            <input type="password" id="password" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="••••••••">
            <p class="text-xs text-gray-500 mt-1">Minimum 6 characters</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
            <input type="text" id="company" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Your Company">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Referral Code (Optional)</label>
            <input type="text" id="referralCode" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter referral code">
          </div>
          
          <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-purple-700 transition duration-200 transform hover:scale-105">
            🚀 Create Account
          </button>
        </form>
        
        <div class="mt-6 text-center">
          <p class="text-gray-600">Already have an account? 
            <a href="/user-login.html" class="text-blue-600 hover:text-blue-800 font-semibold">Sign In</a>
          </p>
        </div>
        
        <!-- Success/Error Messages -->
        <div id="message" class="mt-4 p-4 rounded-lg hidden"></div>
      </div>

      <!-- Features -->
      <div class="space-y-6">
        <div class="glass-effect rounded-2xl p-6 feature-card">
          <div class="flex items-center mb-4">
            <div class="bg-green-100 p-3 rounded-full mr-4">
              <span class="text-2xl">🆓</span>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-800">Free Starter Plan</h3>
              <p class="text-gray-600">50 URLs & QR codes per month</p>
            </div>
          </div>
          <ul class="text-gray-700 space-y-2">
            <li>✅ 50 short URLs per month</li>
            <li>✅ 50 QR codes per month</li>
            <li>✅ 30-day analytics</li>
            <li>✅ Community support</li>
          </ul>
        </div>

        <div class="glass-effect rounded-2xl p-6 feature-card">
          <div class="flex items-center mb-4">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <span class="text-2xl">💎</span>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-800">Pro Plan - $9.99/month</h3>
              <p class="text-gray-600">Unlimited URLs with advanced features</p>
            </div>
          </div>
          <ul class="text-gray-700 space-y-2">
            <li>✅ Unlimited URLs & QR codes</li>
            <li>✅ Custom QR designs</li>
            <li>✅ API access (10K requests)</li>
            <li>✅ 1-year analytics</li>
            <li>✅ Priority support</li>
          </ul>
        </div>

        <div class="glass-effect rounded-2xl p-6 feature-card">
          <div class="flex items-center mb-4">
            <div class="bg-purple-100 p-3 rounded-full mr-4">
              <span class="text-2xl">🏢</span>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-800">Business Plan - $29.99/month</h3>
              <p class="text-gray-600">Perfect for teams and businesses</p>
            </div>
          </div>
          <ul class="text-gray-700 space-y-2">
            <li>✅ Everything in Pro</li>
            <li>✅ Team collaboration (20 members)</li>
            <li>✅ API access (50K requests)</li>
            <li>✅ White-label options</li>
            <li>✅ Advanced analytics</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="text-center mt-8">
      <a href="/" class="text-white hover:text-gray-200 transition duration-200">
        ← Back to Home
      </a>
    </div>
  </div>

  <script>
    document.getElementById('registerForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const messageDiv = document.getElementById('message');
      const submitButton = e.target.querySelector('button[type="submit"]');
      
      // Get form data
      const formData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        password: document.getElementById('password').value,
        firstName: document.getElementById('firstName').value,
        lastName: document.getElementById('lastName').value,
        company: document.getElementById('company').value,
        referralCode: document.getElementById('referralCode').value
      };
      
      // Disable submit button
      submitButton.disabled = true;
      submitButton.textContent = '🔄 Creating Account...';
      
      try {
        const response = await fetch('/api/user/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
          // Success
          messageDiv.className = 'mt-4 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
          messageDiv.textContent = '🎉 Account created successfully! Redirecting to dashboard...';
          messageDiv.classList.remove('hidden');
          
          // Store token
          localStorage.setItem('userToken', data.token);
          
          // Redirect to dashboard
          setTimeout(() => {
            window.location.href = '/dashboard.html';
          }, 2000);
          
        } else {
          // Error
          messageDiv.className = 'mt-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
          messageDiv.textContent = '❌ ' + data.error;
          messageDiv.classList.remove('hidden');
        }
        
      } catch (error) {
        messageDiv.className = 'mt-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
        messageDiv.textContent = '❌ Network error. Please try again.';
        messageDiv.classList.remove('hidden');
      }
      
      // Re-enable submit button
      submitButton.disabled = false;
      submitButton.textContent = '🚀 Create Account';
    });
  </script>
</body>
</html>
